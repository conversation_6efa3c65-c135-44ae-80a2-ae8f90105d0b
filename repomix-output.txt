This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: .repomix/bundles.json, src, .env, .gitignore, nodemon.json, package-lock.json, package.json, tsconfig.json
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)


================================================================
Directory Structure
================================================================
.repomix/
  bundles.json
src/
  services/
    api.ts
    auth.ts
  types/
    index.ts
  server.ts
.gitignore
nodemon.json
package.json
tsconfig.json

================================================================
Files
================================================================

================
File: .repomix/bundles.json
================
{
  "bundles": {}
}

================
File: src/services/auth.ts
================
// Simple server-side authentication
import { AuthState } from "../types";

const SECURITY_CODE = process.env.SECURITY_CODE || "1234"; // In production, use environment variables

export const authenticate = async (code: string): Promise<boolean> => {
  return code === SECURITY_CODE;
};

export const validateAuth = (authHeader?: string): boolean => {
  if (!authHeader) return false;
  const token = authHeader.replace("Bearer ", "");
  return token === SECURITY_CODE;
};

================
File: src/server.ts
================
import express from "express";
import cors from "cors";
import { apiService } from "./services/api";
import { authenticate } from "./services/auth"; // You'll need to create this
import "dotenv/config";
const app = express();
const port = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

app.post("/api/auth/validate", async (req, res) => {
  const { code } = req.body;
  const result = await authenticate(code);
  res.json({ success: result });
});

// Health check endpoint
app.get("/health", (_, res) => {
  res.json({ status: "healthy" });
});

// Auth endpoint
app.post("/auth", async (req, res) => {
  const { code } = req.body;
  const result = await authenticate(code);
  res.json({ success: result });
});

// API endpoints
app.post("/generate", async (req, res) => {
  try {
    const result = await apiService.generateOrders(req.body);
    res.json(result);
  } catch (error) {
    // To this:
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unknown error occurred" });
    }
  }
});

// Add other endpoints...

app.listen(port, () => {
  console.log(`Server running on http://localhost:${port}`);
});

================
File: nodemon.json
================
{
  "watch": [
    "src"
  ],
  "ext": "ts,json",
  "ignore": [
    "src/**/*.test.ts"
  ],
  "exec": "ts-node src/server.ts"
}

================
File: src/services/api.ts
================
import axios from "axios";
import {
  Client,
  ProcessedClient,
  EmailResult,
  OrderHistory,
} from "../types/index";

const API_BASE_URL = process.env.API_BASE_URL || "http://localhost:3001/api";

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000, // 2 minutes timeout for scraping
});

export const apiService = {
  // Generate order summaries by scraping Shein carts
  async generateOrders(requestData: {
    clients: Client[];
    exchange_rate: number;
    orderDate: string;
    currency: {
      from: string;
      to: string;
    };
  }): Promise<{
    success: boolean;
    message: string;
    results: ProcessedClient[];
  }> {
    const response = await api.post("/generate", requestData);
    return response.data;
  },

  // Send emails to selected clients
  async sendEmails(selectedClients: ProcessedClient["data"][]): Promise<{
    success: boolean;
    message: string;
    results: EmailResult[];
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  }> {
    const response = await api.post("/send", { selectedClients });
    return response.data;
  },

  // Get order history
  async getOrderHistory(): Promise<{
    success: boolean;
    orders: OrderHistory[];
  }> {
    const response = await api.get("/history");
    return response.data;
  },

  // Get specific order by ID
  async getOrder(id: string): Promise<{
    success: boolean;
    order: OrderHistory;
  }> {
    const response = await api.get(`/history/${id}`);
    return response.data;
  },

  // Delete order by ID
  async deleteOrder(id: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await api.delete(`/history/${id}`);
    return response.data;
  },

  // Health check
  async healthCheck(): Promise<{
    status: string;
    message: string;
  }> {
    const response = await api.get("/health");
    return response.data;
  },
};

export default apiService;

================
File: src/types/index.ts
================
// Updated interfaces to match your database structure and component usage

export interface Client {
  id: string;
  name: string;
  email: string;
  cartLinks: string[];
}

export interface Product {
  name: string;
  price: string;
  quantity: number;
  rawPrice: string;
}

export interface ProcessedClient {
  client: string;
  email: string;
  success: boolean;
  data?: {
    name: string;
    email: string;
    products: Product[];
    totalUsd: number;
    totalMur: number;
    cartCount: number;
    exchangeRate: number;
    productCount: number;
  };
  error?: string;
}

export interface EmailResult {
  client: string;
  email: string;
  success: boolean;
  messageId?: string;
  error?: string;
}

// This represents a single row from your database
export interface OrderHistoryRow {
  id: string;
  timestamp: string;
  name: string;
  client_email: string; // Note: using client_email to match your component
  total_usd: number;
  total_mur: number;
  exchange_rate: number;
  product_count: number;
  email_success: number;
  email_message_id?: string;
}

// This represents the grouped structure your component creates
export interface GroupedOrder {
  id: string;
  timestamp: string;
  clients: {
    name: string;
    email: string;
    totalUsd: number;
    totalMur: number;
    exchangeRate: number;
    productCount: number;
  }[];
  emailResults: {
    client: string;
    email: string;
    success: boolean;
    messageId?: string;
  }[];
}

// If you want to keep the OrderHistory name, use this instead:
export type OrderHistory = OrderHistoryRow;

// Authentication types
export interface AuthState {
  isAuthenticated: boolean;
  timestamp: number;
}

================
File: .gitignore
================
node_modules/
dist/
.env

================
File: package.json
================
{
  "name": "shein-order-backend",
  "version": "1.0.0",
  "type": "commonjs",
  "scripts": {
    "dev": "NODE_ENV=development ts-node src/server.ts",
    "build": "tsc",
    "start": "NODE_ENV=production node dist/server.js"
  },
  "dependencies": {
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^16.6.1",
    "express": "^4.18.2"
  },
  "devDependencies": {
    "@types/cors": "^2.8.19",
    "@types/express": "^4.17.23",
    "@types/node": "^20.19.4",
    "ts-node": "^10.9.2",
    "typescript": "^5.8.3"
  }
}

================
File: tsconfig.json
================
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "rootDir": "./src",
    "outDir": "./dist",
    "esModuleInterop": true,
    "strict": true,
    "skipLibCheck": true,
    "moduleResolution": "node"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
}




================================================================
End of Codebase
================================================================
