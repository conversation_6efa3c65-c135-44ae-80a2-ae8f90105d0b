import { useState, useEffect } from "react";
import { ShoppingCart, History, LogOut } from "lucide-react";
import ClientForm from "./components/ClientForm";
import ResultsDisplay from "./components/ResultsDisplay";
import EmailSection from "./components/EmailSection";
import OrderHistory from "./components/OrderHistory";
import SecurityPage from "./components/SecurityPage";
import { Client, ProcessedClient } from "./types";
import { isAuthenticated, clearAuth } from "./utils/auth";

function App() {
  const [activeTab, setActiveTab] = useState<"form" | "history">("form");
  const [clients, setClients] = useState<Client[]>([]);
  const [results, setResults] = useState<ProcessedClient[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [authenticated, setAuthenticated] = useState<boolean>(false);

  // Check authentication status on component mount
  useEffect(() => {
    setAuthenticated(isAuthenticated());
  }, []);

  const handleAuthenticated = () => {
    setAuthenticated(true);
  };

  const handleLogout = () => {
    clearAuth();
    setAuthenticated(false);
  };

  const tabs = [
    { id: "form", label: "Nouvelle Commande", icon: ShoppingCart },
    { id: "history", label: "Historique", icon: History },
  ];

  // Show security page if not authenticated
  if (!authenticated) {
    return <SecurityPage onAuthenticated={handleAuthenticated} />;
  }

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm border-b w-full">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="bg-primary-500 p-2 rounded-lg">
                <ShoppingCart className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Assistant Commandes Shein
                </h1>
                <p className="text-sm text-gray-600">
                  Gérez facilement les commandes de vos clients
                </p>
              </div>
            </div>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              title="Se déconnecter"
            >
              <LogOut className="h-5 w-5" />
              <span className="hidden sm:inline">Déconnexion</span>
            </button>
          </div>
        </div>
      </header>
      {/* Navigation Tabs */}+{" "}
      <nav className="bg-white border-b w-full mt-[64px]">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as "form" | "history")}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? "border-primary-500 text-primary-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </nav>
      {/* Main Content */}
      <main className="w-full px-4 sm:px-6 lg:px-8 py-8 pt-[112px]">
        {activeTab === "form" ? (
          <div className="space-y-8">
            {/* Client Form */}
            <ClientForm
              clients={clients}
              setClients={setClients}
              onResultsUpdate={setResults}
              isLoading={isLoading}
              setIsLoading={setIsLoading}
            />

            {/* Results Display */}
            {results.length > 0 && <ResultsDisplay results={results} />}

            {/* Email Section */}
            {results.some((r) => r.success) && (
              <EmailSection results={results} />
            )}
          </div>
        ) : (
          <OrderHistory />
        )}
      </main>
    </div>
  );
}

export default App;
