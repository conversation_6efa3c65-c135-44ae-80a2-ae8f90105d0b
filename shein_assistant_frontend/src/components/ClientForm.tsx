import React, { useState } from "react";
import {
  Plus,
  Minus,
  Loader2,
  <PERSON>,
  Link as LinkIcon,
  ShoppingCart,
} from "lucide-react";
import { Client, ProcessedClient } from "../types";
import apiService from "../services/api";

interface ClientFormProps {
  clients: Client[];
  setClients: (clients: Client[]) => void;
  onResultsUpdate: (results: ProcessedClient[]) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const ClientForm: React.FC<ClientFormProps> = ({
  clients,
  setClients,
  onResultsUpdate,
  isLoading,
  setIsLoading,
}) => {
  const [error, setError] = useState<string>("");
  const [exchange_rate, setexchange_rate] = useState<number>(40); // USD to MUR default rate
  const [orderDate, setOrderDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [fromCurrency, setFromCurrency] = useState<string>("USD");
  const [toCurrency, setToCurrency] = useState<string>("MUR");

  const addClient = () => {
    const newClient: Client = {
      id: Date.now().toString(),
      name: "",
      email: "",
      cartLinks: [""],
    };
    setClients([...clients, newClient]);
  };

  const removeClient = (id: string) => {
    setClients(clients.filter((client) => client.id !== id));
  };

  const updateClient = (id: string, field: keyof Client, value: any) => {
    setClients(
      clients.map((client) =>
        client.id === id ? { ...client, [field]: value } : client
      )
    );
  };

  const addCartLink = (clientId: string) => {
    setClients(
      clients.map((client) =>
        client.id === clientId
          ? { ...client, cartLinks: [...client.cartLinks, ""] }
          : client
      )
    );
  };

  const removeCartLink = (clientId: string, linkIndex: number) => {
    setClients(
      clients.map((client) =>
        client.id === clientId
          ? {
              ...client,
              cartLinks: client.cartLinks.filter((_, i) => i !== linkIndex),
            }
          : client
      )
    );
  };

  const updateCartLink = (
    clientId: string,
    linkIndex: number,
    value: string
  ) => {
    setClients(
      clients.map((client) =>
        client.id === clientId
          ? {
              ...client,
              cartLinks: client.cartLinks.map((link, i) =>
                i === linkIndex ? value : link
              ),
            }
          : client
      )
    );
  };

  const validateForm = (): string | null => {
    if (clients.length === 0) {
      return "Veuillez ajouter au moins un client";
    }

    if (!exchange_rate || exchange_rate <= 0) {
      return "Le taux de change doit être supérieur à 0";
    }

    if (!orderDate) {
      return "La date de commande est requise";
    }

    for (const client of clients) {
      if (!client.name.trim()) {
        return "Tous les clients doivent avoir un nom";
      }
      if (!client.email.trim() || !client.email.includes("@")) {
        return "Tous les clients doivent avoir un email valide";
      }
      if (client.cartLinks.filter((link) => link.trim()).length === 0) {
        return `${client.name} doit avoir au moins un lien de panier`;
      }
    }

    return null;
  };

  const handleGenerate = async () => {
    setError("");

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);

    try {
      const requestData = {
        clients,
        exchange_rate,
        orderDate,
        currency: {
          from: fromCurrency,
          to: toCurrency,
        },
      };

      const response = await apiService.generateOrders(requestData);

      if (response.success) {
        onResultsUpdate(response.results);
        setError("");
      } else {
        setError("Erreur lors de la génération des commandes");
      }
    } catch (error: any) {
      console.error("Generate error:", error);
      setError(
        error.response?.data?.message || "Erreur de connexion au serveur"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Users className="h-6 w-6 text-primary-500" />
          <h2 className="text-xl font-semibold text-gray-900">
            Informations Clients
          </h2>
        </div>
        <button
          onClick={addClient}
          className="flex items-center space-x-2 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Ajouter Client</span>
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Global Settings */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Paramètres de Commande
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de Commande *
            </label>
            <input
              type="date"
              value={orderDate}
              onChange={(e) => setOrderDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Devise Source
            </label>
            <select
              value={fromCurrency}
              onChange={(e) => setFromCurrency(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="USD">USD ($)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Devise Cible
            </label>
            <select
              value={toCurrency}
              onChange={(e) => setToCurrency(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="MUR">MUR (Rs)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Taux de Change ({fromCurrency}/{toCurrency}) *
            </label>
            <input
              type="number"
              step="0.01"
              value={exchange_rate}
              onChange={(e) =>
                setexchange_rate(parseFloat(e.target.value) || 0)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="40.00"
              min="0.01"
            />
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {clients.map((client, clientIndex) => (
          <div
            key={client.id}
            className="border border-gray-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Client {clientIndex + 1}
              </h3>
              {clients.length > 1 && (
                <button
                  onClick={() => removeClient(client.id)}
                  className="text-red-500 hover:text-red-700 p-1"
                >
                  <Minus className="h-4 w-4" />
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom *
                </label>
                <input
                  type="text"
                  value={client.name}
                  onChange={(e) =>
                    updateClient(client.id, "name", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Nom du client"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  value={client.email}
                  onChange={(e) =>
                    updateClient(client.id, "email", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Liens Paniers Shein *
                </label>
                <button
                  onClick={() => addCartLink(client.id)}
                  className="flex items-center space-x-1 text-primary-500 hover:text-primary-700 text-sm"
                >
                  <Plus className="h-3 w-3" />
                  <span>Ajouter lien</span>
                </button>
              </div>

              <div className="space-y-2">
                {client.cartLinks.map((link, linkIndex) => (
                  <div key={linkIndex} className="flex items-center space-x-2">
                    <LinkIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                    <input
                      type="url"
                      value={link}
                      onChange={(e) =>
                        updateCartLink(client.id, linkIndex, e.target.value)
                      }
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="https://fr.shein.com/cart/..."
                    />
                    {client.cartLinks.length > 1 && (
                      <button
                        onClick={() => removeCartLink(client.id, linkIndex)}
                        className="text-red-500 hover:text-red-700 p-1"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}

        {clients.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg mb-2">Aucun client ajouté</p>
            <p className="text-sm">
              Cliquez sur "Ajouter Client" pour commencer
            </p>
          </div>
        )}
      </div>

      {clients.length > 0 && (
        <div className="mt-6 pt-6 border-t">
          <button
            onClick={handleGenerate}
            disabled={isLoading}
            className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Génération en cours...</span>
              </>
            ) : (
              <>
                <ShoppingCart className="h-5 w-5" />
                <span>Générer les Totaux</span>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default ClientForm;
