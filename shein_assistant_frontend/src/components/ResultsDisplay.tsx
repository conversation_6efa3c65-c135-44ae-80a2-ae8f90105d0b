import { CheckCircle, XCircle, Package, DollarSign } from "lucide-react";
import { ProcessedClient } from "../types";

interface ResultsDisplayProps {
  results: ProcessedClient[];
}

const ResultsDisplay = ({ results }: ResultsDisplayProps) => {
  const successfulResults = results.filter((r) => r.success && r.data);
  const failedResults = results.filter((r) => !r.success);

  const total_usd = successfulResults.reduce(
    (sum, r) => sum + (r.data?.totalUsd || 0),
    0
  );
  const total_mur = successfulResults.reduce(
    (sum, r) => sum + (r.data?.totalMur || 0),
    0
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center space-x-3 mb-6">
        <Package className="h-6 w-6 text-green-500" />
        <h2 className="text-xl font-semibold text-gray-900">
          Résultats de l'Analyse
        </h2>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-blue-500" />
            <span className="text-sm font-medium text-blue-700">Succès</span>
          </div>
          <p className="text-2xl font-bold text-blue-900">
            {successfulResults.length}
          </p>
        </div>

        <div className="bg-red-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <XCircle className="h-5 w-5 text-red-500" />
            <span className="text-sm font-medium text-red-700">Échecs</span>
          </div>
          <p className="text-2xl font-bold text-red-900">
            {failedResults.length}
          </p>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-green-500" />
            <span className="text-sm font-medium text-green-700">
              Total USD
            </span>
          </div>
          <p className="text-2xl font-bold text-green-900">
            ${total_usd.toFixed(2)}
          </p>
        </div>

        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-700">
              Total MUR
            </span>
          </div>
          <p className="text-2xl font-bold text-yellow-900">
            Rs {total_mur.toLocaleString()}
          </p>
        </div>
      </div>

      {/* Successful Results */}
      {successfulResults.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Commandes Traitées avec Succès
          </h3>
          <div className="space-y-4">
            {successfulResults.map((result, index) => (
              <div
                key={index}
                className="border border-green-200 rounded-lg p-4 bg-green-50"
              >
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {result.data?.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {result.data?.email}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-green-700">
                      ${result.data?.totalUsd?.toFixed(2) || "0.00"}
                    </p>
                    <p className="text-sm text-gray-600">
                      Rs {result.data?.totalMur?.toLocaleString() || "0"}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Taux:</span>
                    <span className="ml-1 font-medium text-gray-500">
                      {result.data?.exchangeRate}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Paniers:</span>
                    <span className="ml-1 font-medium text-gray-500">
                      {result.data?.cartCount}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Produits:</span>
                    <span className="ml-1 font-medium text-gray-500">
                      {result.data?.productCount}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-green-700 font-medium">Traité</span>
                  </div>
                </div>

                {/* Product Details */}
                {result.data?.products && result.data.products.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-green-200">
                    <details className="group">
                      <summary className="cursor-pointer text-sm font-medium text-green-700 hover:text-green-800">
                        Voir les produits ({result.data.products.length})
                      </summary>
                      <div className="mt-2 space-y-3">
                        {result.data.products.map((product, productIndex) => (
                          <div
                            key={productIndex}
                            className="bg-white p-4 rounded-md border border-gray-200 shadow-sm"
                          >
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <div className="col-span-2">
                                <p className="font-medium text-gray-800 mb-2">
                                  {product.name}
                                </p>
                              </div>
                              <div className="flex flex-col md:items-end">
                                <div className="flex space-x-3 mb-1">
                                  <span className="bg-blue-50 px-3 py-1 rounded-full text-blue-700 font-medium">
                                    Qté: {product.quantity}
                                  </span>
                                  <span className="bg-green-50 px-3 py-1 rounded-full text-green-700 font-medium">
                                    {product.price}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Failed Results */}
      {failedResults.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Erreurs de Traitement
          </h3>
          <div className="space-y-3">
            {failedResults.map((result, index) => (
              <div
                key={index}
                className="border border-red-200 rounded-lg p-4 bg-red-50"
              >
                <div className="flex items-start space-x-3">
                  <XCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">
                      {result.client}
                    </h4>
                    <p className="text-sm text-red-600 mt-1">{result.error}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {results.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>Aucun résultat à afficher</p>
        </div>
      )}
    </div>
  );
};

export default ResultsDisplay;
