import React, { useState } from "react";
import { Lock, ShoppingCart, Eye, EyeOff } from "lucide-react";
import { authenticate } from "../utils/auth";

interface SecurityPageProps {
  onAuthenticated: () => void;
}

const SecurityPage: React.FC<SecurityPageProps> = ({ onAuthenticated }) => {
  const [code, setCode] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showCode, setShowCode] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    // Add a small delay to prevent brute force attempts
    await new Promise((resolve) => setTimeout(resolve, 500));

    if (code.length !== 4) {
      setError("Le code doit contenir exactement 4 caractères");
      setIsLoading(false);
      return;
    }

    const isValid = await authenticate(code);

    if (isValid) {
      onAuthenticated();
    } else {
      setError("Code incorrect. Veuillez réessayer.");
      setCode("");
    }

    setIsLoading(false);
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.slice(0, 4); // Limit to 4 characters
    setCode(value);
    if (error) setError(""); // Clear error when user starts typing
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Logo/Header */}
        <div className="text-center mb-8">
          <div className="bg-primary-500 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
            <ShoppingCart className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Assistant Commandes Shein
          </h1>
          <p className="text-gray-600">Accès sécurisé requis</p>
        </div>

        {/* Security Form */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-6">
            <Lock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Code d'accès
            </h2>
            <p className="text-gray-600 text-sm">
              Veuillez entrer le code à 4 caractères pour accéder à
              l'application
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="security-code" className="sr-only">
                Code de sécurité
              </label>
              <div className="relative">
                <input
                  id="security-code"
                  type={showCode ? "text" : "password"}
                  value={code}
                  onChange={handleCodeChange}
                  placeholder="••••"
                  className={`w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    error
                      ? "border-red-300 bg-red-50"
                      : "border-gray-300 bg-white text-gray-500"
                  }`}
                  maxLength={4}
                  autoComplete="off"
                  autoFocus
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowCode(!showCode)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  {showCode ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {error && (
                <p className="mt-2 text-sm text-red-600 text-center">{error}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={code.length !== 4 || isLoading}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                code.length === 4 && !isLoading
                  ? "bg-primary-600 hover:bg-primary-700 text-white"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Vérification...
                </div>
              ) : (
                "Accéder"
              )}
            </button>
          </form>

          {/* Security notice */}
          <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-xs text-blue-800 text-center">
              🔒 Code de sécurité requis pour accéder à l'application
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500">
            Gérez facilement les commandes de vos clients
          </p>
        </div>
      </div>
    </div>
  );
};

export default SecurityPage;
