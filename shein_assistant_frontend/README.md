# 🛍️ Shein Order Assistant - Frontend

A modern React frontend application for managing Shein orders for clients. This application provides an intuitive interface for processing cart data, calculating currency conversions, and managing order history.

## ✨ Features

- **🔐 Secure Authentication**: 4-character code protection with 24-hour sessions
- **👥 Client Management**: Add and manage multiple clients with their information
- **🛒 Cart Processing**: Interface for processing Shein cart data and calculations
- **💱 Currency Display**: View totals in USD and converted amounts in MUR
- **📧 Email Management**: Send personalized order summaries to clients
- **📊 Order History**: Track and view detailed order records
- **📱 Responsive Design**: Modern UI that works on all devices
- **⚡ Real-time Updates**: Live status updates and notifications

## 🏗️ Tech Stack

- **React 18** with TypeScript for type safety
- **Vite** for fast development and optimized builds
- **TailwindCSS** for modern, responsive styling
- **Lucide React** for beautiful icons
- **Axios** for API communication
- **ESLint** for code quality

## 📋 Prerequisites

- **Node.js 18+** installed on your system
- **Backend API** running (separate repository)
- Modern web browser (Chrome, Firefox, Safari, Edge)

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd shein_assistant_frontend

# Install dependencies
npm install
```

### 2. Environment Configuration

Create a `.env.local` file in the root directory:

```env
# API Base URL - Update this to point to your backend
VITE_API_BASE_URL=http://localhost:3001/api

# For production deployment:
# VITE_API_BASE_URL=https://your-backend-url.onrender.com/api
```

### 3. Development

```bash
# Start development server
npm run dev

# The application will be available at:
# http://localhost:3000
```

### 4. Building for Production

```bash
# Build the application
npm run build

# Preview the production build
npm run preview

# The built files will be in the 'dist' directory
```

## 🔧 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build for production |
| `npm run preview` | Preview production build locally |
| `npm run lint` | Run ESLint for code quality checks |
| `npm run serve` | Serve production build using serve |

## 🔐 Authentication & Security

### How Authentication Works
1. **Security Code Entry**: Users enter a 4-character code on the security page
2. **Server Validation**: Code is validated against the backend API
3. **Session Management**: Valid sessions last 24 hours
4. **Auto-logout**: Sessions expire automatically for security

### Security Features
- ✅ Server-side validation only
- ✅ No security code stored in frontend
- ✅ Automatic session expiration
- ✅ Secure session storage
- ✅ Manual logout option

## 📖 User Guide

### 1. Getting Started
1. **Access the Application**: Navigate to the frontend URL
2. **Enter Security Code**: Input your 4-character access code
3. **Main Dashboard**: Access the order management interface

### 2. Managing Clients
- **Add Client**: Click "Ajouter Client" to add new clients
- **Client Information**: Enter name, email, and exchange rate
- **Cart Links**: Add multiple Shein cart URLs per client
- **Remove Client**: Use the minus button to remove clients

### 3. Processing Orders
- **Generate Totals**: Click "Générer les Totaux" to process all carts
- **View Results**: See processed data with USD and MUR amounts
- **Product Details**: Expand to view individual products
- **Error Handling**: Failed processes are clearly marked

### 4. Email Management
- **Select Clients**: Choose which clients to send emails to
- **Send Emails**: Click "Envoyer les Emails" to send summaries
- **Track Status**: Monitor email delivery in real-time
- **View Results**: See successful and failed email attempts

### 5. Order History
- **View Orders**: Switch to "Historique" tab
- **Order Details**: Click on orders to see full details
- **Client Information**: View all client data for each order
- **Email Status**: See which emails were sent successfully
- **Delete Orders**: Remove old orders when needed

## 🎨 UI Components

### Main Components
- **SecurityPage**: Authentication interface
- **ClientForm**: Client management and cart input
- **ResultsDisplay**: Order processing results
- **EmailSection**: Email management interface
- **OrderHistory**: Historical order data

### Design System
- **Colors**: Primary red theme with semantic colors
- **Typography**: Clean, readable font hierarchy
- **Spacing**: Consistent spacing using Tailwind utilities
- **Icons**: Lucide React icons for consistency
- **Responsive**: Mobile-first responsive design

## 🔌 API Integration

### API Endpoints Used
```typescript
// Authentication
POST /api/auth/validate

// Order Processing
POST /api/generate

// Email Management
POST /api/send

// Order History
GET /api/history
GET /api/history/:id
DELETE /api/history/:id

// Health Check
GET /api/health
```

### Error Handling
- **Network Errors**: Graceful handling of connection issues
- **API Errors**: Clear error messages for users
- **Validation**: Client-side validation before API calls
- **Retry Logic**: Automatic retries for failed requests

## 🚀 Deployment

### Free Hosting Options

#### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard:
# VITE_API_BASE_URL=https://your-backend-url.onrender.com/api
```

#### Netlify
```bash
# Build the project
npm run build

# Deploy dist folder to Netlify
# Set environment variable:
# VITE_API_BASE_URL=https://your-backend-url.onrender.com/api
```

#### GitHub Pages
```bash
# Install gh-pages
npm install --save-dev gh-pages

# Add to package.json scripts:
# "deploy": "gh-pages -d dist"

# Build and deploy
npm run build
npm run deploy
```

### Render.com
The project includes a `render.yaml` file for easy deployment:

```yaml
services:
  - type: web
    name: shein-assistant-frontend
    env: node
    buildCommand: npm install && npm run build
    startCommand: npm run preview
    envVars:
      - key: NODE_ENV
        value: production
      - key: VITE_API_BASE_URL
        value: "https://your-backend-url.onrender.com/api"
```

## 🛠️ Development

### Project Structure
```
src/
├── components/          # React components
│   ├── ClientForm.tsx   # Client management
│   ├── EmailSection.tsx # Email interface
│   ├── OrderHistory.tsx # Order history
│   ├── ResultsDisplay.tsx # Results display
│   └── SecurityPage.tsx # Authentication
├── services/           # API services
│   └── api.ts         # API client
├── types/             # TypeScript types
│   └── index.ts       # Type definitions
├── utils/             # Utility functions
│   ├── auth.ts        # Authentication utilities
│   └── __tests__/     # Unit tests
├── App.tsx            # Main application
├── main.tsx           # Application entry point
└── index.css          # Global styles
```

### Code Quality
- **TypeScript**: Full type safety
- **ESLint**: Code quality enforcement
- **Prettier**: Code formatting (via ESLint)
- **Modern React**: Hooks and functional components
- **Clean Architecture**: Separation of concerns

### Testing
```bash
# Run authentication tests (development)
# Tests are available in src/utils/__tests__/
```

## 🔍 Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Vite cache
rm -rf node_modules/.vite
```

#### API Connection Issues
- ✅ Verify backend is running
- ✅ Check VITE_API_BASE_URL in .env.local
- ✅ Ensure CORS is configured on backend
- ✅ Check browser network tab for errors

#### Authentication Problems
- ✅ Verify security code with backend admin
- ✅ Check browser localStorage for session data
- ✅ Try clearing browser cache and cookies
- ✅ Ensure backend authentication endpoint is working

#### Styling Issues
```bash
# Rebuild Tailwind CSS
npm run build

# Check for conflicting CSS
# Verify Tailwind classes are correct
```

## 📱 Browser Support

- **Chrome**: ✅ Latest 2 versions
- **Firefox**: ✅ Latest 2 versions
- **Safari**: ✅ Latest 2 versions
- **Edge**: ✅ Latest 2 versions
- **Mobile**: ✅ iOS Safari, Chrome Mobile

## � Updates & Maintenance

### Updating Dependencies
```bash
# Check for updates
npm outdated

# Update all dependencies
npm update

# Update specific package
npm install package-name@latest
```

### Performance Optimization
- **Code Splitting**: Automatic with Vite
- **Tree Shaking**: Dead code elimination
- **Asset Optimization**: Image and CSS optimization
- **Caching**: Browser caching for static assets

## 📝 License

This project is for personal use. Feel free to modify and adapt for your needs.

## 🤝 Contributing

This is a personal project, but suggestions and improvements are welcome!

### Development Guidelines
1. **Code Style**: Follow existing patterns
2. **TypeScript**: Maintain type safety
3. **Components**: Keep components focused and reusable
4. **Testing**: Add tests for new features
5. **Documentation**: Update README for changes

---

**Built with ❤️ using modern React and TypeScript**

> **Note**: This is the frontend application only. You'll need the corresponding backend API for full functionality.
