#!/usr/bin/env node

// Simple API testing script
const axios = require("axios");

const BASE_URL = "http://localhost:3001";
// Load environment variables to get the correct security code
require("dotenv").config();
const SECURITY_CODE = process.env.SECURITY_CODE || "7330"; // From .env file

// Test configuration
const testClient = {
  id: "test-1",
  name: "Test Client",
  email: "<EMAIL>",
  cartLinks: ["https://example.com/cart1", "https://example.com/cart2"],
};

const testOrderData = {
  clients: [testClient],
  exchange_rate: 45.5,
  orderDate: new Date().toISOString(),
  currency: {
    from: "USD",
    to: "MUR",
  },
};

// Helper function to make API calls
async function apiCall(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || "NO_RESPONSE",
      error: error.response?.data || error.message,
    };
  }
}

// Test functions
async function testHealthCheck() {
  console.log("\n🔍 Testing Health Check Endpoints...");

  const tests = [
    { name: "Basic Health Check", endpoint: "/health" },
    { name: "API Health Check", endpoint: "/api/health" },
  ];

  for (const test of tests) {
    const result = await apiCall("GET", test.endpoint);
    console.log(
      `  ${test.name}: ${result.success ? "✅" : "❌"} (${result.status})`
    );
    if (!result.success) {
      console.log(`    Error: ${JSON.stringify(result.error)}`);
    }
  }
}

async function testAuthentication() {
  console.log("\n🔐 Testing Authentication...");

  const tests = [
    { name: "Valid Auth Code", code: SECURITY_CODE, shouldPass: true },
    { name: "Invalid Auth Code", code: "wrong-code", shouldPass: false },
    { name: "Missing Auth Code", code: "", shouldPass: false },
  ];

  for (const test of tests) {
    const result = await apiCall("POST", "/api/auth/validate", {
      code: test.code,
    });

    let passed = false;
    if (test.shouldPass) {
      // For valid auth, expect success response with success: true
      passed = result.success && result.data?.success === true;
    } else {
      // For invalid auth, expect either success response with success: false, or error response
      passed =
        (result.success && result.data?.success === false) ||
        (!result.success && result.status >= 400);
    }

    console.log(`  ${test.name}: ${passed ? "✅" : "❌"} (${result.status})`);
    if (!passed) {
      console.log(
        `    Expected success: ${test.shouldPass}, Got: ${result.data?.success}`
      );
      console.log(
        `    Response: ${JSON.stringify(result.data || result.error)}`
      );
    }
  }
}

async function testOrderGeneration() {
  console.log("\n📦 Testing Order Generation...");

  const tests = [
    { name: "Valid Order Data", data: testOrderData, shouldPass: true },
    {
      name: "Missing Clients",
      data: { ...testOrderData, clients: null },
      shouldPass: false,
    },
    {
      name: "Invalid Exchange Rate",
      data: { ...testOrderData, exchange_rate: "invalid" },
      shouldPass: false,
    },
  ];

  for (const test of tests) {
    const result = await apiCall("POST", "/api/generate", test.data);

    let passed = false;
    if (test.shouldPass) {
      // For valid data, expect success response with success: true
      passed = result.success && result.data?.success === true;
    } else {
      // For invalid data, expect either success response with success: false, or error response
      passed =
        (result.success && result.data?.success === false) ||
        (!result.success && result.status >= 400);
    }

    console.log(`  ${test.name}: ${passed ? "✅" : "❌"} (${result.status})`);
    if (!passed) {
      console.log(
        `    Expected success: ${test.shouldPass}, Got: ${result.data?.success}`
      );
      console.log(
        `    Response: ${JSON.stringify(result.data || result.error)}`
      );
    }
  }
}

async function testEmailSending() {
  console.log("\n📧 Testing Email Sending...");

  const mockClientData = [
    {
      name: "Test Client",
      email: "<EMAIL>",
      products: [],
      totalUsd: 100,
      totalMur: 4550,
      cartCount: 1,
      exchangeRate: 45.5,
      productCount: 5,
    },
  ];

  const tests = [
    {
      name: "Valid Email Data",
      data: { selectedClients: mockClientData },
      shouldPass: true,
    },
    { name: "Missing Selected Clients", data: {}, shouldPass: false },
    {
      name: "Invalid Selected Clients",
      data: { selectedClients: "invalid" },
      shouldPass: false,
    },
  ];

  for (const test of tests) {
    const result = await apiCall("POST", "/api/send", test.data);
    const passed = result.success && result.data?.success === test.shouldPass;
    console.log(`  ${test.name}: ${passed ? "✅" : "❌"} (${result.status})`);
    if (!passed) {
      console.log(
        `    Expected success: ${test.shouldPass}, Got: ${result.data?.success}`
      );
      console.log(
        `    Response: ${JSON.stringify(result.data || result.error)}`
      );
    }
  }
}

async function testOrderHistory() {
  console.log("\n📚 Testing Order History...");

  const tests = [
    { name: "Get All Orders", method: "GET", endpoint: "/api/history" },
    {
      name: "Get Specific Order",
      method: "GET",
      endpoint: "/api/history/test-123",
    },
    {
      name: "Delete Order",
      method: "DELETE",
      endpoint: "/api/history/test-123",
    },
  ];

  for (const test of tests) {
    const result = await apiCall(test.method, test.endpoint);
    console.log(
      `  ${test.name}: ${result.success ? "✅" : "❌"} (${result.status})`
    );
    if (!result.success) {
      console.log(`    Error: ${JSON.stringify(result.error)}`);
    }
  }
}

async function testInvalidEndpoints() {
  console.log("\n❌ Testing Invalid Endpoints...");

  const tests = [
    { name: "Non-existent API endpoint", endpoint: "/api/nonexistent" },
    {
      name: "Invalid method on valid endpoint",
      method: "PUT",
      endpoint: "/api/health",
    },
  ];

  for (const test of tests) {
    const result = await apiCall(test.method || "GET", test.endpoint);
    const is404 = result.status === 404;
    console.log(`  ${test.name}: ${is404 ? "✅" : "❌"} (${result.status})`);
    if (!is404) {
      console.log(`    Expected 404, got: ${result.status}`);
    }
  }
}

// Main test runner
async function runAllTests() {
  console.log("🚀 Starting API Tests...");
  console.log(`Testing server at: ${BASE_URL}`);

  try {
    await testHealthCheck();
    await testAuthentication();
    await testOrderGeneration();
    await testEmailSending();
    await testOrderHistory();
    await testInvalidEndpoints();

    console.log("\n✅ All API tests completed!");
    console.log(
      "\nNote: Some endpoints are currently using mock implementations."
    );
    console.log("Replace mock logic with actual business logic as needed.");
  } catch (error) {
    console.error("\n❌ Test runner failed:", error.message);
    process.exit(1);
  }
}

// Check if server is running before starting tests
async function checkServerStatus() {
  try {
    await apiCall("GET", "/health");
    console.log("✅ Server is running");
    return true;
  } catch (error) {
    console.log("❌ Server is not running. Please start the server first:");
    console.log("   npm run dev");
    return false;
  }
}

// Run tests
(async () => {
  const serverRunning = await checkServerStatus();
  if (serverRunning) {
    await runAllTests();
  }
})();
