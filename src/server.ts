// Load environment variables first
require("dotenv").config();

import express from "express";
import cors from "cors";
import { authenticate } from "./services/auth";
import { Client, ProcessedClient, EmailResult, OrderHistory } from "./types";

const app = express();
const port = process.env.PORT || 3001;

// CORS configuration (single, consolidated setup)
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
  })
);

app.use(express.json());

// Health check endpoint
app.get("/health", (_, res) => {
  res.json({
    status: "healthy",
    message: "Server is running",
    timestamp: new Date().toISOString(),
  });
});

// API Health check endpoint (for consistency with api.ts)
app.get("/api/health", (_, res) => {
  res.json({
    status: "healthy",
    message: "API is running",
    timestamp: new Date().toISOString(),
  });
});

// Authentication endpoint
app.post("/api/auth/validate", async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: "Security code is required",
      });
    }

    const isValid = await authenticate(code);
    res.json({
      success: isValid,
      message: isValid ? "Authentication successful" : "Invalid code",
    });
  } catch (error) {
    console.error("Auth error:", error);
    res.status(500).json({
      success: false,
      message: "Authentication service error",
    });
  }
});

// Generate orders endpoint
app.post("/api/generate", async (req, res) => {
  try {
    const { clients, exchange_rate, orderDate, currency } = req.body;

    // Validate required fields
    if (!clients || !Array.isArray(clients)) {
      return res.status(400).json({
        success: false,
        message: "Clients array is required",
      });
    }

    if (!exchange_rate || typeof exchange_rate !== "number") {
      return res.status(400).json({
        success: false,
        message: "Valid exchange rate is required",
      });
    }

    // Mock implementation - replace with actual scraping logic
    const results: ProcessedClient[] = clients.map((client: Client) => ({
      client: client.name,
      email: client.email,
      success: true,
      data: {
        name: client.name,
        email: client.email,
        products: [], // This would be populated by scraping logic
        totalUsd: 0,
        totalMur: 0,
        cartCount: client.cartLinks.length,
        exchangeRate: exchange_rate,
        productCount: 0,
      },
    }));

    res.json({
      success: true,
      message: "Orders generated successfully",
      results,
    });
  } catch (error) {
    console.error("Generate orders error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// Send emails endpoint
app.post("/api/send", async (req, res) => {
  try {
    const { selectedClients } = req.body;

    if (!selectedClients || !Array.isArray(selectedClients)) {
      return res.status(400).json({
        success: false,
        message: "Selected clients array is required",
      });
    }

    // Mock implementation - replace with actual email sending logic
    const results: EmailResult[] = selectedClients.map((client: any) => ({
      client: client.name,
      email: client.email,
      success: true,
      messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    }));

    const summary = {
      total: results.length,
      successful: results.filter((r) => r.success).length,
      failed: results.filter((r) => !r.success).length,
    };

    res.json({
      success: true,
      message: "Emails sent successfully",
      results,
      summary,
    });
  } catch (error) {
    console.error("Send emails error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// Get order history endpoint
app.get("/api/history", async (req, res) => {
  try {
    // Mock implementation - replace with actual database query
    const orders: OrderHistory[] = [];

    res.json({
      success: true,
      orders,
    });
  } catch (error) {
    console.error("Get history error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// Get specific order endpoint
app.get("/api/history/:id", async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required",
      });
    }

    // Mock implementation - replace with actual database query
    const order: OrderHistory = {
      id,
      timestamp: new Date().toISOString(),
      name: "Mock Order",
      client_email: "<EMAIL>",
      total_usd: 0,
      total_mur: 0,
      exchange_rate: 1,
      product_count: 0,
      email_success: 1,
    };

    res.json({
      success: true,
      order,
    });
  } catch (error) {
    console.error("Get order error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// Delete order endpoint
app.delete("/api/history/:id", async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required",
      });
    }

    // Mock implementation - replace with actual database deletion
    res.json({
      success: true,
      message: `Order ${id} deleted successfully`,
    });
  } catch (error) {
    console.error("Delete order error:", error);
    if (error instanceof Error) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        error: "An unknown error occurred",
      });
    }
  }
});

// 404 handler for unknown API routes
app.use("/api/*", (req, res) => {
  res.status(404).json({
    success: false,
    message: `API endpoint ${req.path} not found`,
  });
});

// Global error handler
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    console.error("Global error:", err);
    res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
);

app.listen(port, () => {
  console.log(`Server running on http://localhost:${port}`);
  console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(
    `Frontend URL: ${process.env.FRONTEND_URL || "http://localhost:3000"}`
  );
});
