import express from "express";
import cors from "cors";
import { apiService } from "./services/api";
import { authenticate } from "./services/auth"; // You'll need to create this
import "dotenv/config";
const app = express();
const port = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// Add this after creating the app
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    credentials: true,
  })
);

// Replace the existing /auth endpoint with this:
app.post("/api/auth/validate", async (req, res) => {
  const { code } = req.body;
  const isValid = await authenticate(code);
  res.json({
    success: isValid,
    message: isValid ? "Authentication successful" : "Invalid code",
  });
});

// Remove the old /auth endpoint
// Health check endpoint
app.get("/health", (_, res) => {
  res.json({ status: "healthy" });
});

// Auth endpoint
app.post("/auth", async (req, res) => {
  const { code } = req.body;
  const result = await authenticate(code);
  res.json({ success: result });
});

// API endpoints
app.post("/generate", async (req, res) => {
  try {
    const result = await apiService.generateOrders(req.body);
    res.json(result);
  } catch (error) {
    // To this:
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unknown error occurred" });
    }
  }
});

// Add other endpoints...

app.listen(port, () => {
  console.log(`Server running on http://localhost:${port}`);
});
