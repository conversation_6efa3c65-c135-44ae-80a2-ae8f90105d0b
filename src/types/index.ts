// Updated interfaces to match your database structure and component usage

export interface Client {
  id: string;
  name: string;
  email: string;
  cartLinks: string[];
}

export interface Product {
  name: string;
  price: string;
  quantity: number;
  rawPrice: string;
}

export interface ProcessedClient {
  client: string;
  email: string;
  success: boolean;
  data?: {
    name: string;
    email: string;
    products: Product[];
    totalUsd: number;
    totalMur: number;
    cartCount: number;
    exchangeRate: number;
    productCount: number;
  };
  error?: string;
}

export interface EmailResult {
  client: string;
  email: string;
  success: boolean;
  messageId?: string;
  error?: string;
}

// This represents a single row from your database
export interface OrderHistoryRow {
  id: string;
  timestamp: string;
  name: string;
  client_email: string; // Note: using client_email to match your component
  total_usd: number;
  total_mur: number;
  exchange_rate: number;
  product_count: number;
  email_success: number;
  email_message_id?: string;
}

// This represents the grouped structure your component creates
export interface GroupedOrder {
  id: string;
  timestamp: string;
  clients: {
    name: string;
    email: string;
    totalUsd: number;
    totalMur: number;
    exchangeRate: number;
    productCount: number;
  }[];
  emailResults: {
    client: string;
    email: string;
    success: boolean;
    messageId?: string;
  }[];
}

// If you want to keep the OrderHistory name, use this instead:
export type OrderHistory = OrderHistoryRow;

// Authentication types
export interface AuthState {
  isAuthenticated: boolean;
  timestamp: number;
}
